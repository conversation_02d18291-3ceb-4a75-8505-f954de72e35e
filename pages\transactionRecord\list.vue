<template>
	<view class="transaction-list">
		<!-- 交易记录列表 -->
		<view class="record-section">
			<view class="record-header">
				<text class="record-title">交易记录</text>
				<text class="record-count">共{{ transactionList.length }}条记录</text>
			</view>
			
			<view class="record-list" v-if="transactionList.length > 0">
				<view class="record-item" v-for="(item, index) in transactionList" :key="index">
					<view class="record-main">
						<view class="record-info">
							<view class="record-device">
								<text class="iconfont">&#xe613;</text>
								<text class="device-text">设备编号：{{ item.deviceNumber }}</text>
							</view>
							<view class="record-time">
								<text class="iconfont">&#xe854;</text>
								<text class="time-text">{{ item.transactionTime }}</text>
							</view>
						</view>
						<view class="record-amount">
							<text class="amount-value" :class="{'amount-income': item.type === 'recharge', 'amount-expense': item.type === 'consume'}">
								{{ item.type === 'recharge' ? '+' : '-' }}¥{{ item.amount }}
							</text>
							<text class="amount-type">{{ item.typeName }}</text>
						</view>
					</view>
					<view class="record-details">
						<text class="detail-text">交易流水号：{{ item.transactionId }}</text>
						<text class="detail-text" v-if="item.waterVolume">用水量：{{ item.waterVolume }}L</text>
						<text class="detail-text">余额：¥{{ item.balance }}</text>
					</view>
				</view>
			</view>
			
			<view class="empty-state" v-else>
				<text class="iconfont">&#xe708;</text>
				<text class="empty-text">暂无交易记录</text>
				<text class="empty-tip">请调整查询条件后重试</text>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="transactionList.length > 0 && hasMore">
			<button class="load-more-btn" @click="handleLoadMore" :loading="loading">
				{{ loading ? '加载中...' : '加载更多' }}
			</button>
		</view>
	</view>
</template>

<script setup>
// 定义props
const props = defineProps({
	transactionList: {
		type: Array,
		default: () => []
	},
	loading: {
		type: Boolean,
		default: false
	},
	hasMore: {
		type: Boolean,
		default: true
	}
})

// 定义emits
const emit = defineEmits(['loadMore'])

// 加载更多
const handleLoadMore = () => {
	emit('loadMore')
}
</script>

<style lang="scss" scoped>
@import url('./index.min.css');
</style>
