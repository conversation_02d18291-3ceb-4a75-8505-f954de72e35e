<template>
	<!-- 扫码用水 -->
	<view class="function-btn" @click="scanWater">
		<text class="iconfont">&#xe600;</text>
		<text class="btn-text">扫码用水</text>
	</view>
</template>

<script setup>

import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'


// 生命周期钩子
onLoad(() => {
	// 原来的 onLoad 逻辑可以放在这里
})

// 开始扫码
const scanWater = () => {
	console.log('开始扫码')

	uni.scanCode({
		success: async function (res) {
			console.log('条码内容：' + res.result);


		},
		fail: function (err) {
			console.log("err", err);
		}
	});
	// // H5环境下的扫码实现
	// if (process.env.NODE_ENV === 'development' || typeof window !== 'undefined') {
	// 	// H5环境下可以使用第三方扫码库，如 qrcode-reader
	// 	uni.showToast({
	// 		title: 'H5环境下需要集成扫码库',
	// 		icon: 'none',
	// 		duration: 2000
	// 	})
	// } else {
	// 	// 原生环境下使用uni-app的扫码API
	// 	uni.scanCode({
	// 		success: function (res) {
	// 			console.log('扫码结果：' + res.result)
	// 			scanResult.value = res.result
	// 			// 处理扫码结果
	// 			handleScanResult(res.result)
	// 		},
	// 		fail: function (err) {
	// 			console.log('扫码失败：', err)
	// 			uni.showToast({
	// 				title: '扫码失败',
	// 				icon: 'none'
	// 			})
	// 		}
	// 	})
	// }
}

// 处理扫码结果
const handleScanResult = (result) => {
	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${result}`,
		success: function (res) {
			if (res.confirm) {
				// 用户点击确定，可以进行下一步操作
				console.log('用户确认扫码结果')
			}
		}
	})
}

</script>
<style lang="scss" scoped>
.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}
</style>

