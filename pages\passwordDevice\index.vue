<template>
	<view class="content">
		<view class="main">
			<view class="form">
				<!-- 消费密码 -->
				<view class="form-item">
					<view class="label">消费密码</view>
					<view class="password-inputs">
						<view
							v-for="(item, index) in passwordArray"
							:key="index"
							class="password-input-wrapper"
							@click="focusInput(index)"
						>
							<input
								class="password-input"
								type="number"
								:value="passwordArray[index]"
								:focus="focusIndex === index"
								@input="onPasswordInput($event, index)"
								@focus="onPasswordFocus(index)"
								@blur="onPasswordBlur(index)"
								maxlength="1"
								:ref="el => setPasswordRef(el, index)"
							/>
							<text class="password-star" v-if="passwordArray[index]">*</text>
						</view>
					</view>
				</view>

				<!-- 状态选择 -->
				<view class="form-item">
					<view class="label">状态</view>
					<view class="status-options">
						<view
							v-for="item in statusList"
							:key="item.value"
							class="status-option"
							@click="setStatus(item.value)"
						>
							<view class="radio" :class="{ active: form.waterConsumeCodeStatus === item.value }"></view>
							<text class="status-text">{{ item.text }}</text>
						</view>
					</view>
				</view>
			</view>

			<view class="submit">
				<button type="default" class="submit-btn" @click="handleSubmit">保存</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// 状态列表（模拟数据，替代从dict.js导入）
const statusList = [
	{ value: 'ENABLE_TRUE', text: '启用' },
	{ value: 'ENABLE_FALSE', text: '禁用' }
]

// 响应式数据
const passwordArray = reactive(['', '', '', ''])
const passwordRefs = ref([])
const focusIndex = ref(-1) // 当前聚焦的输入框索引
const form = reactive({
	waterConsumeCode: '',
	waterConsumeCodeStatus: 'ENABLE_FALSE'
})

// 设置密码输入框引用
const setPasswordRef = (el, index) => {
	if (el) {
		passwordRefs.value[index] = el
	}
}

// 点击输入框包装器时聚焦输入框
const focusInput = (index) => {
	focusIndex.value = index
}

// 密码输入处理
const onPasswordInput = (event, index) => {
	const value = event.detail.value
	// 只取最后一个字符（防止粘贴多个字符）
	const lastChar = value.slice(-1)

	if (lastChar && /^\d$/.test(lastChar)) {
		passwordArray[index] = lastChar
		// 自动跳转到下一个输入框
		if (index < 3) {
			focusIndex.value = index + 1
		} else {
			// 最后一个框输入完成后失去焦点
			focusIndex.value = -1
		}
	} else if (value === '') {
		// 清空当前框
		passwordArray[index] = ''
	}

	// 更新完整密码
	form.waterConsumeCode = passwordArray.join('')
}

// 密码输入框获得焦点
const onPasswordFocus = (index) => {
	focusIndex.value = index
	// 清空当前输入框的值，准备输入新值
	passwordArray[index] = ''
	// 更新完整密码
	form.waterConsumeCode = passwordArray.join('')
}

// 密码输入框失去焦点
const onPasswordBlur = (index) => {
	if (focusIndex.value === index) {
		focusIndex.value = -1
	}
}

// 自动聚焦到第一个输入框
const autoFocusFirst = () => {
	focusIndex.value = 0
}

// 设置状态
const setStatus = (status) => {
	form.waterConsumeCodeStatus = status
}

// 模拟获取用户信息
const getUserInfo = async () => {
	uni.showLoading({
		title: '加载中...'
	})

	// 模拟API调用
	setTimeout(() => {
		// 模拟从服务器获取的数据
		form.waterConsumeCodeStatus = 'ENABLE_FALSE'
		uni.hideLoading()
	}, 1000)
}

// 提交处理
const handleSubmit = async () => {
	if (!form.waterConsumeCode || form.waterConsumeCode.length < 4) {
		return uni.showToast({
			title: '请输入4位消费密码',
			icon: 'none'
		})
	}

	// 验证是否都是数字
	if (!/^\d{4}$/.test(form.waterConsumeCode)) {
		return uni.showToast({
			title: '密码必须是4位数字',
			icon: 'none'
		})
	}

	uni.showLoading({
		title: '保存中...',
		mask: true
	})

	// 模拟API调用
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '保存成功！',
			duration: 2000,
			mask: true
		})

		console.log('保存的数据:', {
			waterConsumeCode: form.waterConsumeCode,
			waterConsumeCodeStatus: form.waterConsumeCodeStatus
		})

		setTimeout(() => {
			uni.navigateBack()
		}, 2000)
	}, 1500)
}

// 生命周期
onMounted(() => {
	getUserInfo()
	// 延迟一点时间确保DOM渲染完成后再聚焦
	setTimeout(() => {
		autoFocusFirst()
	}, 300)
})
</script>

<style lang="scss" scoped>
.content {
	height: 100%;
	width: 100%;
	background-color: #ffffff;
}

.main {
	padding: 60rpx 40rpx;

	.form {
		.form-item {
			margin-bottom: 80rpx;

			.label {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
				margin-bottom: 40rpx;
			}

			.password-inputs {
				display: flex;
				gap: 30rpx;
				justify-content: center;
			}

			.password-input-wrapper {
				position: relative;
				width: 100rpx;
				height: 120rpx;
				border: 2rpx solid #e0e0e0;
				border-radius: 12rpx;
				background: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
			}

			.password-input-wrapper:focus-within {
				border-color: #4facfe;
			}

			.password-input {
				position: absolute;
				width: 100%;
				height: 100%;
				border: none;
				background: transparent;
				text-align: center;
				font-size: 48rpx;
				font-weight: 600;
				color: transparent;
				outline: none;
				caret-color: #4facfe;
			}

			.password-star {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-size: 48rpx;
				font-weight: bold;
				color: #333333;
				pointer-events: none;
				line-height: 1;
			}

			.status-options {
				display: flex;
				gap: 60rpx;
			}

			.status-option {
				display: flex;
				align-items: center;
				cursor: pointer;
			}

			.radio {
				width: 40rpx;
				height: 40rpx;
				border: 4rpx solid #e0e0e0;
				border-radius: 50%;
				margin-right: 20rpx;
				position: relative;
			}

			.radio.active {
				border-color: #4facfe;
			}

			.radio.active::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 20rpx;
				height: 20rpx;
				background: #4facfe;
				border-radius: 50%;
			}

			.status-text {
				font-size: 32rpx;
				color: #333333;
			}
		}
	}
}

.submit {
	width: 100%;
	padding: 32rpx;
	box-sizing: border-box;
	margin-top: 120rpx;

	.submit-btn {
		width: 100%;
		background: linear-gradient(-90deg, #1B61FF 0%, #397FFF 100%);
		border-radius: 44rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 88rpx;
	}
}
</style>