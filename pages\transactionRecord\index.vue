<template>
	<view class="transaction-record">
		<!-- 搜索栏组件 -->
		<SearchBar
			v-model="searchParams.deviceNumber"
			@search="handleSearch"
			@filter="showFilterModal = true"
		/>

		<!-- 筛选弹窗组件 -->
		<FilterModal
			v-model:visible="showFilterModal"
			:filterParams="filterParams"
			@confirm="handleFilterConfirm"
			@reset="handleFilterReset"
		/>

		<!-- 交易记录列表组件 -->
		<TransactionList
			:transactionList="transactionList"
			:loading="loading"
			:hasMore="hasMore"
			@loadMore="loadMore"
		/>
	</view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import SearchBar from './SearchBar.vue'
import FilterModal from './FilterModal.vue'
import TransactionList from './list.vue'

// 响应式数据
const searchParams = ref({
	deviceNumber: ''
})

// 筛选参数
const filterParams = ref({
	deviceNumber: '',
	startDate: '',
	endDate: ''
})

// 弹窗显示状态
const showFilterModal = ref(false)

// 监听搜索参数中的设备编号变化，同步到筛选参数
watch(() => searchParams.value.deviceNumber, (newVal) => {
	filterParams.value.deviceNumber = newVal
})

// 监听筛选参数中的设备编号变化，同步到搜索参数
watch(() => filterParams.value.deviceNumber, (newVal) => {
	searchParams.value.deviceNumber = newVal
})

const transactionList = ref([
	{
		transactionId: 'TXN202401220001',
		deviceNumber: 'WD001',
		transactionTime: '2024-01-22 09:15:42',
		type: 'consume',
		typeName: '用水消费',
		amount: '2.50',
		waterVolume: '25',
		balance: '126.00'
	},
	{
		transactionId: 'TXN202401200001',
		deviceNumber: '',
		transactionTime: '2024-01-20 14:30:25',
		type: 'recharge',
		typeName: '账户充值',
		amount: '50.00',
		waterVolume: '',
		balance: '128.50'
	},
	{
		transactionId: 'TXN202401180001',
		deviceNumber: 'WD002',
		transactionTime: '2024-01-18 16:45:12',
		type: 'consume',
		typeName: '用水消费',
		amount: '3.20',
		waterVolume: '32',
		balance: '78.50'
	}
])

const loading = ref(false)
const hasMore = ref(true)

// 处理搜索
const handleSearch = (searchValue) => {
	filterParams.value.deviceNumber = searchValue
	console.log('搜索设备编号:', searchValue)
	searchTransactions()
}

// 处理筛选确认
const handleFilterConfirm = (params) => {
	console.log('筛选条件:', params)
	// 更新筛选参数
	filterParams.value = { ...params }
	// 同步设备编号到搜索参数
	searchParams.value.deviceNumber = params.deviceNumber
	// 执行搜索
	searchTransactions()
}

// 处理筛选重置
const handleFilterReset = () => {
	filterParams.value = {
		deviceNumber: '',
		startDate: '',
		endDate: ''
	}
	searchParams.value.deviceNumber = ''
}



// 查询交易记录
const searchTransactions = () => {
	uni.showLoading({
		title: '查询中...'
	})
	console.log('筛选接口', filterParams.value);
	

	// 模拟API调用
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '查询完成',
			icon: 'success'
		})
		// 这里应该调用实际的API查询交易记录
		loadTransactionList()
	}, 1000)
}



// 加载交易记录列表
const loadTransactionList = () => {
	// 这里应该调用实际的API获取交易记录
	console.log('列表接口')
}

// 加载更多
const loadMore = () => {
	if (loading.value) return
	
	loading.value = true
	
	// 模拟API调用
	setTimeout(() => {
		loading.value = false
		// 这里应该加载更多数据
		hasMore.value = false // 模拟没有更多数据
		uni.showToast({
			title: '已加载全部数据',
			icon: 'none'
		})
	}, 1500)
}

// 生命周期钩子
onMounted(() => {
	loadTransactionList()
})
</script>

<style lang="scss" scoped>
@import url('./index.min.css');

.transaction-record {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}
</style>
