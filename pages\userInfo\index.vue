<template>
	<view class="user-info">
		<view class="info-section">
			<!-- 用户基本信息 -->
			<view class="info-card">
				<view class="card-header">
					<text class="iconfont">&#xe796;</text>
					<text class="card-title">用户基本信息</text>
				</view>
				<view class="info-content">
					<view class="info-item">
						<text class="label">用户ID：</text>
						<text class="value">{{ userInfo.userId }}</text>
					</view>
					<view class="info-item">
						<text class="label">用户名：</text>
						<text class="value">{{ userInfo.username }}</text>
					</view>
					<view class="info-item">
						<text class="label">手机号：</text>
						<text class="value">{{ userInfo.phone }}</text>
					</view>
					<view class="info-item">
						<text class="label">注册时间：</text>
						<text class="value">{{ userInfo.registerTime }}</text>
					</view>
				</view>
			</view>

			<!-- 账户状态信息 -->
			<view class="info-card">
				<view class="card-header">
					<text class="iconfont">&#xe657;</text>
					<text class="card-title">账户状态</text>
				</view>
				<view class="info-content">
					<view class="info-item">
						<text class="label">账户状态：</text>
						<text class="value" :class="{'status-active': userInfo.accountStatus === '正常', 'status-inactive': userInfo.accountStatus !== '正常'}">
							{{ userInfo.accountStatus }}
						</text>
					</view>
					<view class="info-item">
						<text class="label">是否绑卡：</text>
						<text class="value" :class="{'status-active': userInfo.isCardBound, 'status-inactive': !userInfo.isCardBound}">
							{{ userInfo.isCardBound ? '已绑定' : '未绑定' }}
						</text>
					</view>
					<view class="info-item" v-if="userInfo.isCardBound">
						<text class="label">绑定卡号：</text>
						<text class="value">{{ userInfo.cardNumber }}</text>
					</view>
				</view>
			</view>

			<!-- 账户余额信息 -->
			<view class="info-card">
				<view class="card-header">
					<text class="iconfont">&#xe652;</text>
					<text class="card-title">账户余额</text>
				</view>
				<view class="info-content">
					<view class="balance-item">
						<text class="balance-label">当前余额</text>
						<text class="balance-value">¥{{ userInfo.balance }}</text>
					</view>
					<view class="info-item">
						<text class="label">最后充值时间：</text>
						<text class="value">{{ userInfo.lastRechargeTime || '暂无记录' }}</text>
					</view>
					<view class="info-item">
						<text class="label">最后消费时间：</text>
						<text class="value">{{ userInfo.lastConsumeTime || '暂无记录' }}</text>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<!-- <view class="button-group">
				<button class="action-btn recharge-btn" @click="goToRecharge">
					<text>账户充值</text>
				</button>
				<button class="action-btn bind-card-btn" @click="goToBindCard" v-if="!userInfo.isCardBound">
					<text>绑定银行卡</text>
				</button>
				<button class="action-btn refresh-btn" @click="refreshUserInfo">
					<text>刷新信息</text>
				</button>
			</view> -->
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const userInfo = ref({
	userId: '10001',
	username: '张三',
	phone: '138****8888',
	registerTime: '2023-01-15',
	accountStatus: '正常',
	isCardBound: true,
	cardNumber: '**** **** **** 1234',
	balance: '128.50',
	lastRechargeTime: '2024-01-20 14:30:25',
	lastConsumeTime: '2024-01-22 09:15:42'
})

// 刷新用户信息
const refreshUserInfo = () => {
	uni.showLoading({
		title: '刷新中...'
	})
	
	// 模拟API调用
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '刷新成功',
			icon: 'success'
		})
		// 这里应该调用实际的API获取最新用户信息
		loadUserInfo()
	}, 1500)
}

// 加载用户信息
const loadUserInfo = () => {
	// 这里应该调用实际的API获取用户信息
	console.log('加载用户信息')
}

// 跳转到充值页面
const goToRecharge = () => {
	uni.showToast({
		title: '充值功能开发中',
		icon: 'none'
	})
}

// 跳转到绑卡页面
const goToBindCard = () => {
	uni.showToast({
		title: '绑卡功能开发中',
		icon: 'none'
	})
}

// 生命周期钩子
onMounted(() => {
	loadUserInfo()
})
</script>
<style lang="scss" scoped>

@import url('./index.min.css');

</style>
