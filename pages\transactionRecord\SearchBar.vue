<template>
	<view class="search-bar">
		<view class="search-input-wrapper">
			<text class="iconfont">&#xe651;</text>
			<input
				class="search-input"
				v-model="searchValue"
				placeholder="设备编号"
				maxlength="50"
				@confirm="handleSearch"
				@input="onInput"
			/>
		</view>
		<view class="search-btn" @click="handleSearch">
			<text class="search-btn-text">搜索</text>
		</view>
		<view class="filter-btn" @click="handleFilter">
			<text class="iconfont">&#xe641;</text>
		</view>
	</view>
</template>

<script setup>
import { ref, watch } from 'vue'

// 定义props
const props = defineProps({
	modelValue: {
		type: String,
		default: ''
	}
})

// 定义emits
const emit = defineEmits(['update:modelValue', 'search', 'filter'])

// 内部搜索值
const searchValue = ref(props.modelValue)

// 监听props变化
watch(() => props.modelValue, (newVal) => {
	searchValue.value = newVal
})

// 输入事件
const onInput = () => {
	emit('update:modelValue', searchValue.value)
}

// 搜索事件
const handleSearch = () => {
	emit('search', searchValue.value)
}

// 筛选事件
const handleFilter = () => {
	emit('filter')
}
</script>

<style lang="scss" scoped>

@import url('./index.min.css');

</style>

