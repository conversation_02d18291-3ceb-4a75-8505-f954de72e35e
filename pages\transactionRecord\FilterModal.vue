<template>
	<view class="filter-modal" v-if="visible" @click="handleClose">
		<view class="filter-content" @click.stop>
			<view class="filter-header">
				<text class="filter-title">筛选</text>
				<view class="close-btn" @click="handleClose">
					<text class="iconfont">&#xe60c;</text>
				</view>
			</view>
			
			<view class="filter-body">
				<view class="filter-item">
					<text class="filter-label">设备编号</text>
					<input
						class="filter-input"
						v-model="tempParams.deviceNumber"
						placeholder="请输入设备编号"
						maxlength="20"
					/>
				</view>
				
				<view class="filter-item">
					<text class="filter-label">时间范围</text>
					<view class="date-range-wrapper">
						<picker 
							mode="date" 
							:value="tempParams.startDate" 
							@change="onStartDateChange"
						>
							<view class="date-picker-box">
								<text class="date-text">{{tempParams.startDate || '开始日期'}}</text>
							</view>
						</picker>
						<text class="date-separator">至</text>
						<picker 
							mode="date" 
							:value="tempParams.endDate" 
							@change="onEndDateChange"
						>
							<view class="date-picker-box">
								<text class="date-text">{{tempParams.endDate || '结束日期'}}</text>
							</view>
						</picker>
					</view>
				</view>
			</view>
			
			<view class="filter-footer">
				<button class="reset-btn" @click="handleReset">重置</button>
				<button class="confirm-btn" @click="handleConfirm">查询</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, watch } from 'vue'

// 定义props
const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	filterParams: {
		type: Object,
		default: () => ({
			deviceNumber: '',
			startDate: '',
			endDate: ''
		})
	}
})

// 定义emits
const emit = defineEmits(['update:visible', 'confirm', 'reset'])

// 临时参数
const tempParams = ref({
	deviceNumber: '',
	startDate: '',
	endDate: ''
})

// 监听props变化
watch(() => props.filterParams, (newVal) => {
	tempParams.value = { ...newVal }
}, { immediate: true, deep: true })

// 开始日期选择
const onStartDateChange = (e) => {
	tempParams.value.startDate = e.detail.value
}

// 结束日期选择
const onEndDateChange = (e) => {
	tempParams.value.endDate = e.detail.value
}

// 关闭弹窗
const handleClose = () => {
	emit('update:visible', false)
}

// 重置
const handleReset = () => {
	tempParams.value = {
		deviceNumber: '',
		startDate: '',
		endDate: ''
	}
	emit('reset')
}

// 确认
const handleConfirm = () => {
	// 验证日期范围
	if (tempParams.value.startDate && tempParams.value.endDate) {
		if (tempParams.value.startDate > tempParams.value.endDate) {
			uni.showToast({
				title: '开始日期不能大于结束日期',
				icon: 'none'
			})
			return
		}
	}
	
	emit('confirm', { ...tempParams.value })
	emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
@import url('./index.min.css');
</style>

