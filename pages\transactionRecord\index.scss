.transaction-record {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}

.search-bar {
	display: flex;
	align-items: center;
	background: #ffffff;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f8f8;
	border-radius: 8rpx;
	padding: 0 20rpx;
	height: 70rpx;
}

.search-icon {
	font-size: 28rpx;
	color: #999999;
	margin-right: 10rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	background: transparent;
	border: none;
	outline: none;
}

.search-btn {
	margin-left: 15rpx;
	padding: 0 25rpx;
	height: 70rpx;
	background: #1A73E8;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	white-space: nowrap;
}

.search-btn-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
}

.filter-btn {
	margin-left: 15rpx;
	.iconfont{
		font-size: 50rpx;
	}
}



/* 筛选弹窗样式 */
.filter-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 999;
}

.filter-content {
	width: 100%;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.filter-header {
	padding: 30rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.filter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.close-btn {
	position: absolute;
	right: 40rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.close-btn .iconfont {
	font-size: 36rpx;
	color: #999999;
}

.filter-body {
	padding: 40rpx;
}

.filter-item {
	margin-bottom: 40rpx;
}

.filter-item:last-child {
	margin-bottom: 0;
}

.filter-label {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 20rpx;
	font-weight: 500;
}

.filter-input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	font-size: 28rpx;
	color: #333333;
	box-sizing: border-box;
}

.date-range-wrapper {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.date-picker-box {
	flex: 1;
	height: 80rpx;
	padding: 0 20rpx;
	background: #f8f8f8;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	min-width: 200rpx;
}

.date-text {
	font-size: 28rpx;
	color: #333333;
	text-align: center;
}

.date-separator {
	font-size: 24rpx;
	color: #999999;
	white-space: nowrap;
	flex-shrink: 0;
}

.filter-footer {
	display: flex;
	padding: 20rpx 40rpx 40rpx;
	gap: 20rpx;
}

.reset-btn,
.confirm-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 10rpx;
	font-size: 28rpx;
	border: none;
	cursor: pointer;
}

.reset-btn {
	background: #f5f5f5;
	color: #666666;
}

.confirm-btn {
	background: #1A73E8;
	color: #ffffff;
}





.device-input {
	flex: 1;
	height: 100%;
	border: none;
	background: transparent;
	font-size: 28rpx;
	color: #333333;
	outline: none;
}

.device-input::placeholder {
	color: #999999;
}



.record-section {
	padding: 0 20rpx 20rpx;
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.record-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.record-count {
	font-size: 24rpx;
	color: #999999;
}

.record-item {
	background: #ffffff;
	border-radius: 15rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.record-main {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.record-info {
	flex: 1;
}

.record-device, .record-time {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.record-device .iconfont, .record-time .iconfont {
	font-size: 32rpx;
	color: #8360c3;
	margin-right: 10rpx;
}

.device-text, .time-text {
	font-size: 26rpx;
	color: #666666;
}

.record-amount {
	text-align: right;
}

.amount-value {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 5rpx;
}

.amount-income {
	color: #52c41a;
}

.amount-expense {
	color: #ff4d4f;
}

.amount-type {
	font-size: 24rpx;
	color: #999999;
}

.record-details {
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.detail-text {
	display: block;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 8rpx;
}

.detail-text:last-child {
	margin-bottom: 0;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	background: #ffffff;
	border-radius: 15rpx;
}

.empty-state .iconfont {
	font-size: 80rpx;
	color: #d9d9d9;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 10rpx;
}

.empty-tip {
	font-size: 24rpx;
	color: #999999;
}

.load-more {
	padding: 20rpx;
}

.load-more-btn {
	width: 100%;
	height: 70rpx;
	background: #f5f5f5;
	border: none;
	border-bottom: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	font-size: 28rpx;
	color: #666666;
}