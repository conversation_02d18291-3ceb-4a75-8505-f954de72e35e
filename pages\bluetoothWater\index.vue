<template>
	<!-- 蓝牙用水 -->
	<view class="function-btn" @click="bluetoothWater">
		<text class="iconfont">&#xe63e;</text>
		<text class="btn-text">蓝牙用水</text>
	</view>
</template>

<script setup>

import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 生命周期钩子
onLoad(() => {
	// 原来的 onLoad 逻辑可以放在这里
})

const bluetoothWater = () => {
	console.log('蓝牙用水')
	// 跳转到蓝牙用水页面
}
</script>

<style lang="scss" scoped>
.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}
</style>
